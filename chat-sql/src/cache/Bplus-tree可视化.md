有一个基类, 具有与B+树实现细节无关的基本方法, 比如:
- cmd: 与动画序列相关的命令

```javascript
Algorithm.prototype.cmd = function()
{
	if (this.recordAnimation)
	{
		var command = arguments[0];
		for(i = 1; i < arguments.length; i++)
		{
			command = command + "<;>" + String(arguments[i]);
		}
		this.commands.push(command);
	}
}
```

然后在B+树操作的时候通过:cmd插入序列 (print的部分为例:)

```javascript
this.cmd("SetHighlight", tmp.graphicID, 1);
this.cmd("Step"); // 设置断点
		while (!tmp.isLeaf)
		{
			this.cmd("SetEdgeHighlight", tmp.graphicID, tmp.children[0].graphicID, 1);
			this.cmd("Step");
			this.cmd("SetHighlight", tmp.graphicID, 0);
			this.cmd("SetHighlight", tmp.children[0].graphicID, 1);
			this.cmd("SetEdgeHighlight", tmp.graphicID, tmp.children[0].graphicID, 0);
			this.cmd("Step");
			tmp = tmp.children[0];				
		}
```

## B+树操作相关

### 插入

将插入操作分为两部分——初始化和递归执行部分

#### 初始化

作为插入操作的入口函数, 负责:

- 初始化一个空的指令序列 `commands`
- 处理插入为空树的特殊情况
- 在递归结束时候指令序列返回给动画管理器处理

```javascript
this.commands = new Array();
	
	this.cmd("SetText", this.messageID, "Inserting " + insertedValue);
	this.cmd("Step");
	
	if (this.treeRoot == null)
	{
		this.treeRoot = new BTreeNode(this.nextIndex++, this.starting_x, STARTING_Y);
		this.cmd("CreateBTreeNode",this.treeRoot.graphicID, WIDTH_PER_ELEM, NODE_HEIGHT, 1, this.starting_x, STARTING_Y, BACKGROUND_COLOR,  FOREGROUND_COLOR);
		this.treeRoot.keys[0] = insertedValue;
		this.cmd("SetText", this.treeRoot.graphicID, insertedValue, 0);
	}
	else
	{
		this.insert(this.treeRoot, insertedValue);					
		if (!this.treeRoot.isLeaf)
		{
			this.resizeTree();
		}
	}
	
	this.cmd("SetText", this.messageID, "");
	
	return this.commands;
```



#### 递归插入

首先, 我们高亮当前处理的节点并设置断点:

```javascript
this.cmd("SetHighlight", tree.graphicID, 1);
this.cmd("Step");
```

然后, 根据内部节点和叶子节点进行不同的处理:

- 如果当前在内部节点之间比较:

```javascript
var findIndex = 0;
		while (findIndex < tree.numKeys && tree.keys[findIndex] < insertValue)
		{
			findIndex++;					
		}				
		this.cmd("SetEdgeHighlight", tree.graphicID, tree.children[findIndex].graphicID, 1);
		this.cmd("Step");
		this.cmd("SetEdgeHighlight", tree.graphicID, tree.children[findIndex].graphicID, 0);
		this.cmd("SetHighlight", tree.graphicID, 0);
		this.insert(tree.children[findIndex], insertValue);	
```

> 找到内部的位置, 在迭代插入到子节点之前, 通过 `cmd` 设置边的高亮



- 如果是插入到叶子节点:

```javascript
this.cmd("SetText", this.messageID, "Inserting " + insertValue + ".  Inserting into a leaf");
		tree.numKeys++;
		this.cmd("SetNumElements", tree.graphicID, tree.numKeys);
		var insertIndex = tree.numKeys - 1;
		while (insertIndex > 0 && tree.keys[insertIndex - 1] > insertValue)
		{
			tree.keys[insertIndex] = tree.keys[insertIndex - 1];
			this.cmd("SetText", tree.graphicID, tree.keys[insertIndex], insertIndex);
			insertIndex--;
		}
		tree.keys[insertIndex] = insertValue;
		this.cmd("SetText", tree.graphicID, tree.keys[insertIndex], insertIndex);
		this.cmd("SetHighlight", tree.graphicID, 0);
		if (tree.next != null)
		{
			this.cmd("Disconnect", tree.graphicID, tree.next.graphicID);
			this.cmd("Connect", tree.graphicID, 
				tree.next.graphicID,
				FOREGROUND_COLOR,
				0, // Curve
				1, // Directed
				"", // Label
				tree.numKeys);
		}
		this.resizeTree();
		this.insertRepair(tree);
```

倒数第二部的 `resizeTree`主要用于计算可视化所需的要素, 比如动画结束之后各个节点的位置.



最后一个函数 `insertRepair`主要用于递归检查是否需要分裂节点

#### insertRepair

```javascript
BPlusTree.prototype.insertRepair = function(tree) 
{
	if (tree.numKeys <= this.max_keys)
	{
		return;
	}
	else if (tree.parent == null)
	{
		this.treeRoot = this.split(tree);
		return;
	}
	else
	{
		var newNode  = this.split(tree);
		this.insertRepair(newNode);
	}			
}
```

其中, `split`用于将当前节点分裂:

#### split

当需要分裂节点时, 我们先通过 `cmd` 来输出相关的提示信息并高亮节点:

```javascript
this.cmd("SetText", this.messageID, "Node now contains too many keys.  Splittig ...");
this.cmd("SetHighlight", tree.graphicID, 1);
this.cmd("Step");
this.cmd("SetHighlight", tree.graphicID, 0);
```

然后我们初始化分裂的到右节点和上浮的key:

```javascript
// 使用nextIndex用于标识节点的唯一id(用于可视化)
var rightNode = new BTreeNode(this.nextIndex++, tree.x + 100, tree.y);

// split_index也是初始化B+树的时候得到的, 因为分裂的时候, 上浮节点的index由阶数M决定
var risingNode = tree.keys[this.split_index];
```

比如, 在初始化的时候设置 `this.split_index = Math.floor((newDegree) / 2);`

之后, 我们需要填充右侧节点内部的key:

```javascript
for (i =rightSplit; i < tree.numKeys; i++)
	{
		rightNode.keys[i - rightSplit] = tree.keys[i];
		this.cmd("SetText", rightNode.graphicID, rightNode.keys[i -rightSplit], i - rightSplit);
}
```

同时, “清除”左侧内部节点的右侧部分:

```javascript
for (i = this.split_index; i < tree.numKeys; i++)
{
  this.cmd("SetText", tree.graphicID, "", i); 
}
```

> 并不实际将keys内部的元素置零, 而是在可视化角度将其清除, 这样就可以实现 **undo** 



### 删除

与insert类似, 删除也有入口函数和递归函数

#### 入口函数 `deleteElement`

在删除之后, 检查当前root的key是否为空:

```javascript
if (this.treeRoot.numKeys == 0)
	{
		this.cmd("Delete", this.treeRoot.graphicID);
		this.treeRoot = this.treeRoot.children[0];
		this.treeRoot.parent = null;
		this.resizeTree();
	}
```



#### 递归删除 `doDelete`

**处理删除的key是第一个元素的特殊情况:**

```javascript
while (parentNode != null)
{
  if (parentIndex > 0 && parentNode.keys[parentIndex - 1] == val)
  {
    parentNode.keys[parentIndex - 1] = nextSmallest;
    this.cmd("SetText", parentNode.graphicID, parentNode.keys[parentIndex - 1], parentIndex - 1);								
  }
  var grandParent = parentNode.parent;
  for (parentIndex = 0; grandParent != null && grandParent.children[parentIndex] != parentNode; parentIndex++);
  parentNode = grandParent;
}
```

如果删除的是节点的第一个元素, 就需要检查是否在父节点中需要删除.

假设记当前遍历(也就是删除元素所在的)节点为 `tree`, 我们遍历其parent, 找到对应的 `parentIndex`(子节点在父节点中对应的索引) , 满足下面条件的时候, 就需要将父节点的对应key修改为 `tree` 的下一个最小key:

```javascript
if (parentIndex > 0 && parentNode.keys[parentIndex - 1] == val){
  parentNode.keys[parentIndex - 1] = nextSmallest;
  this.cmd("SetText", parentNode.graphicID, parentNode.keys[parentIndex - 1], parentIndex - 1);								
}
```

由于这个最小key的存在可能是跨层级的, 因此我们需要用一个 `while` 循环遍历:

![image-20250718194352673](Bplus-tree可视化.assets/image-20250718194352673.png)

```javascript
var grandParent = parentNode.parent;

// 遍历得到父节点在祖父节点中的index
for (parentIndex = 0; grandParent != null && grandParent.children[parentIndex] != parentNode; parentIndex++);

parentNode = grandParent;
```

---

#### repairAfterDelete

只有叶子层级的删除需要调用 `this.repairAfterDelete(tree);`, 用于检查是否需要合并节点

如果父节点为空, 那么就是只有一个叶子节点的平凡状态.

否则, 我们依旧遍历找到当前节点在父节点中的索引 `parentIndex`:

- 如果在中间位置, 分别查看左侧和右侧的兄弟节点是否有多余的key

  ```javascript
  if (parentIndex > 0 && parentNode.children[parentIndex - 1].numKeys > this.min_keys)
  {
    this.stealFromLeft(tree, parentIndex);
  }
  ```

  

- 如果在最左侧或者最右侧, 并且由于已经经过第一步的检查, 那么此时一定是兄弟节点无法给出多余的key, 此时需要与其合并:

  ```javascript
  else if (parentIndex == 0)
  {
    // Merge with right sibling
    var nextNode = this.mergeRight(tree);
    this.repairAfterDelete(nextNode.parent);			
  }
  ```

  > 由于合并会导致父节点的key减少, 相当于删除, 因此也需调用删除后检查恢复的函数



#### stealFromLeft

**参数**: `function(tree, parentIndex) ` , 也就是当前节点及其在父节点中的index

无论是否为内部节点, 总是将内部的key先右移, 并且将 `numKeys`计数自增:

```javascript
for (i = tree.numKeys - 1; i > 0; i--)
{
  tree.keys[i] = tree.keys[i-1];
  this.cmd("SetText", tree.graphicID, tree.keys[i], i);
}
```



**处理key:**

如果是叶子节点, 第一个key来自于左侧兄弟的最后一个key, 也即是 `parentNode.keys[parentIndex - 1];`

**需要注意的是**, 如果是内部节点, 第一个key来自于父节点**下放**的之前的一个key, 然后将左兄弟的最大key补充到父节点下放的位置

```javascript
tree.keys[0] = parentNode.keys[parentIndex - 1];
parentNode.keys[parentIndex-1] = leftSib.keys[leftSib.numKeys - 1];				
```



**处理children**:

由于叶子节点没有children需要重新分配, 因此这一部分只针对内部节点. 现将当前节点的孩子右移, 然后将左侧的最后一个孩子赋值:

```javascript
tree.children[0] = leftSib.children[leftSib.numKeys];
```



#### mergeRight



在内部节点合并中，父节点中分隔 `tree` 和 `rightSib` 的那个键 (`parentNode.keys[parentIndex]`) 会被“下放”到 `tree` 和 `rightSib` 合并后的新节点中

同时, 需要将父节点的`children`数组左移更新:

```javascript
parentNode.children[i] = parentNode.children[i+1];
parentNode.keys[i-1] = parentNode.keys[i];
```



## 查找

`findCallback` 将输入的value格式化并清空输入框

`findElement`初始化指令序列并调用递归函数 `findInTree`	

