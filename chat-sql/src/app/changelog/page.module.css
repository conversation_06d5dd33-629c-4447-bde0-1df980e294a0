.container {
  min-height: 100vh;
  padding: 20px;
  position: relative;
  background-color: var(--background);
}

.parallaxBackground {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('/images/grid-pattern.svg');
  background-size: 30px 30px;
  opacity: 0.05;
  z-index: 0;
  pointer-events: none;
}

.content {
  position: relative;
  z-index: 1;
  max-width: 800px;
  margin: 0 auto;
  padding-top: 40px;
}

.yearTitle {
  font-size: 2rem;
  font-weight: 700;
  margin: 2rem 0 1rem;
  color: var(--primary-text);
  position: relative;
  display: inline-block;
}

.yearTitle::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: var(--link-color);
  border-radius: 3px;
}

.monthTitle {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 1.5rem 0 1rem;
  color: var(--primary-text);
}

.commitCard {
  background-color: var(--card-bg) !important;
  border: 1px solid var(--card-border) !important;
  transition: all 0.3s ease !important;
}

.commitCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1) !important;
}

[data-theme="dark"] .commitCard:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3) !important;
}

.commitDetails {
  color: var(--secondary-text) !important;
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.timelineCard {
  background-color: var(--card-bg) !important;
  border: 1px solid var(--card-border) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05) !important;
}

[data-theme="dark"] .timelineCard {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}