'use client';

import React, { useState } from 'react';
import BPlusTreeVisualizerNew from '@/components/BPlusXyflow/BPlusTreeVisualizerNew';
import { Box, Typography, Paper } from '@mui/material';

const BPlusTestPage: React.FC = () => {
  // 1. 提供一组初始数据，而不是从零开始
  const [initialKeys] = useState<number[]>([10, 20, 5, 15, 25, 3, 7]);
  const [order] = useState<number>(3);

  return (
    <Box sx={{ width: '100vw', height: '100vh', display: 'flex', flexDirection: 'column', bgcolor: '#f4f6f8' }}>
      <Paper elevation={2} sx={{ p: 2, m: 2, mb: 0 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          B+树可视化 - 重构版本
        </Typography>
        <Typography variant="body1" color="text.secondary">
          基于指令序列的B+树可视化系统，支持完整的动画控制、步骤导航和断点功能。算法与可视化完全分离，提供更好的用户体验。
        </Typography>
      </Paper>
      
      <Box sx={{ flex: 1, position: 'relative', m: 2, mt: 1 }}>
        <BPlusTreeVisualizerNew
          initialKeys={initialKeys}
          order={order}
        />
      </Box>
    </Box>
  );
};

export default BPlusTestPage;