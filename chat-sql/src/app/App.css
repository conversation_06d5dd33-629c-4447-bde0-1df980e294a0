/* App.css */
.app-container {
    display: flex;
    height: calc(100vh - var(--navbar-height));
    width: 100vw;
    overflow: hidden;
    margin-top: var(--navbar-height);
}

.sidebar-container {
  width: 50px;
  height: 100%;
  flex-shrink: 0;
  z-index: 10;
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  padding-top: var(--navbar-height);
}

.content-container {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.history-panel-container {
  width: 250px;
  height: 100%;
  overflow-y: auto;
  transition: width 0.3s ease;
  border-right: 1px solid var(--sidebar-border);
}

.history-panel-collapsed {
  width: 0;
  overflow: hidden;
}

.editor-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.editor-area {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.sql-editor {
  flex: 1;
  height: 100%;
  overflow: hidden;
  border-right: 1px solid var(--sidebar-border);
}

.result-area {
  flex: 1;
  height: 100%;
  overflow: auto;
  padding: 10px;
  background-color: var(--background);
}

/* 响应式布局 */
@media (max-width: 768px) {
  .editor-area {
    flex-direction: column;
  }
  
  .sql-editor, .result-area {
    flex: none;
    height: 50%;
    width: 100%;
  }
  
  .sql-editor {
    border-right: none;
    border-bottom: 1px solid var(--sidebar-border);
  }
}

/* 确保 LLM 窗口垂直居中 */
.full-height-llm-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  background-color: var(--background);
}

/* 确保内容区域有正确的高度 */
.upper-content, .lower-content {
  height: 100%;
  width: 100%;
  overflow: hidden;
}

/* 确保 DatabaseFlow 容器有正确的高度 */
.database-flow-container {
  width: 100% !important;
  height: 100% !important;
  position: relative;
}


.lower-left-panel{
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.lower-left-content{
  height: 100%;
  width: 100%;
  overflow: hidden;
}