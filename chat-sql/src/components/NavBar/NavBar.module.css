.navBar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: var(--navbar-height);
  background-color: var(--background);
  box-shadow: 0 1px 0px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  padding: 0 20px;
  z-index: 1000;
}

.leftSection {
  flex: 0 0 auto;
}

.middleSection {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
}

.navTabs {
  display: flex;
  gap: 4px;
  align-items: center;
  background: var(--card-bg);
  border-radius: 8px;
  padding: 4px;
  border: 1px solid var(--card-border);
}

.tabButton {
  height: 24px !important;
  padding: 0 16px !important;
  border-radius: 6px !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
  color: var(--secondary-text) !important;
  border: none !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

.tabButton:hover {
  background: var(--button-hover) !important;
  color: var(--primary-text) !important;
}

.activeTab {
  background: var(--button-hover) !important;
  border: var(--button-border) !important;
  box-shadow: 0 2px 4px var(--button-border) !important;
}


.rightSection {
  flex: 0 0 auto;
  display: flex;
  gap: 8px;
  align-items: center;
}

.logoContainer {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logoImage {
  width: 30px;
  height: 30px;
  object-fit: contain;
}

.logoText {
  font-size: 1.2em;
  font-weight: 600;
  color: #4285f4;
  margin: 0;
  font-family: 'Maple Mono', monospace;
}

.navButton{
  border-radius: 50%; 
  border: 1px solid var(--button-border);
  box-shadow: 0 0 10px rgba(136, 134, 134, 0.1);
  height: 30px;
  width: 30px;
  color: var(--icon-color) !important;
}

.navButton:hover {
  background-color: var(--button-hover) !important;
  color: var(--icon-color-hover) !important;
}

[data-theme="dark"] .navButton {
  color: var(--icon-color-dark) !important;
  border-color: var(--button-border);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .navButton:hover {
  color: var(--icon-color-hover) !important;
}

[data-theme="dark"] .navTabs {
  background: var(--card-bg);
  border-color: var(--card-border);
}

[data-theme="dark"] .tabButton {
  color: var(--secondary-text) !important;
}

[data-theme="dark"] .tabButton:hover {
  background: var(--button-hover) !important;
  color: var(--primary-text) !important;
}
