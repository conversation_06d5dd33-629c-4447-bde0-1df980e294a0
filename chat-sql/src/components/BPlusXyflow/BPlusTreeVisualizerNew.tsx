/**
 * 重构后的B+树可视化组件
 * 基于指令序列的动画系统，实现算法与可视化的完全分离
 */

import React, { useEffect, useState, useCallback, useMemo } from 'react';
import {
  ReactFlow,
  useNodesState,
  useEdgesState,
  Controls,
  Background,
  Node,
  Edge,
  BackgroundVariant,
  ReactFlowProvider
} from '@xyflow/react';
import {
  Box,
  Paper,
  TextField,
  Button,
  Typography,
  Alert,
  Grid2 as Grid,
  Divider
} from '@mui/material';
import { Add as AddIcon, Delete as DeleteIcon } from '@mui/icons-material';
import { message } from 'antd';

// 导入新的重构组件
import { BPlusTreeAlgorithm } from '../../lib/bPlusTreeAlgorithm';
import { AnimationManager, AnimationState, AnimationCallbacks } from '../../lib/animationManager';
import { CommandExecutor, CommandExecutorCallbacks } from '../../lib/commandExecutor';
import { BPlusCommand } from '../../lib/bPlusTreeCommands';
import AnimationControls from './AnimationControls';
import BPlusInternalNode from './BPlusInternalNode';
import BPlusLeafNode from './BPlusLeafNode';
import SettingsPanel from './SettingsPanel';
import { BPlusNodeData } from '../utils/bPlusTreeToReactFlow';
import styles from './BPlusTreeVisualizer.module.css';
import '@xyflow/react/dist/style.css';

// 自定义节点类型
const nodeTypes = {
  bPlusInternalNode: BPlusInternalNode,
  bPlusLeafNode: BPlusLeafNode,
};

interface BPlusTreeVisualizerNewProps {
  initialKeys: (number | string)[];
  order: number;
}

// 设置接口
interface Settings {
  isAnimationEnabled: boolean;
  animationSpeed: number;
  order: number;
}

const BPlusTreeVisualizerNew: React.FC<BPlusTreeVisualizerNewProps> = ({
  initialKeys,
  order
}) => {
  // 状态管理
  const [settings, setSettings] = useState<Settings>({
    isAnimationEnabled: true,
    animationSpeed: 500,
    order: order
  });

  const [insertValue, setInsertValue] = useState<string>('');
  const [deleteValue, setDeleteValue] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [isAnimating, setIsAnimating] = useState<boolean>(false);

  // React Flow状态
  const [nodes, setNodes, onNodesChange] = useNodesState<Node<BPlusNodeData>>([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState<Edge>([]);

  // 动画系统状态
  const [animationState, setAnimationState] = useState<AnimationState>({
    currentStep: 0,
    totalSteps: 0,
    isPlaying: false,
    isPaused: false,
    speed: 500
  });

  // 消息API
  const [messageApi, contextHolder] = message.useMessage();

  // 核心实例
  const bPlusTreeAlgorithm = useMemo(() => new BPlusTreeAlgorithm(settings.order), [settings.order]);
  const animationManager = useMemo(() => new AnimationManager(), []);
  const commandExecutor = useMemo(() => {
    const callbacks: CommandExecutorCallbacks = {
      setNodes,
      setEdges,
      showMessage: (msg, type = 'info') => {
        switch (type) {
          case 'success':
            messageApi.success(msg);
            break;
          case 'warning':
            messageApi.warning(msg);
            break;
          case 'error':
            messageApi.error(msg);
            break;
          default:
            messageApi.info(msg);
        }
      }
    };
    return new CommandExecutor(callbacks);
  }, [setNodes, setEdges, messageApi]);

  // 动画管理器回调
  const animationCallbacks: AnimationCallbacks = {
    onStepChange: useCallback(async (step: number, command: BPlusCommand | null) => {
      if (command) {
        await commandExecutor.executeCommand(command);
      }
    }, [commandExecutor]),

    onStateChange: useCallback((state: AnimationState) => {
      setAnimationState(state);
      setIsAnimating(state.isPlaying);
    }, []),

    onComplete: useCallback(() => {
      messageApi.success('动画播放完成');
      setIsAnimating(false);
    }, [messageApi]),

    onError: useCallback((error: Error) => {
      messageApi.error(`动画执行错误: ${error.message}`);
      setIsAnimating(false);
      setError(error.message);
    }, [messageApi])
  };

  // 初始化动画管理器
  useEffect(() => {
    Object.assign(animationManager['callbacks'], animationCallbacks);
    animationManager.setSpeed(settings.animationSpeed);
  }, [animationManager, animationCallbacks, settings.animationSpeed]);

  // 初始化B+树
  useEffect(() => {
    const initializeTree = async () => {
      commandExecutor.reset();
      
      // 插入初始键值
      for (const key of initialKeys) {
        if (typeof key === 'number') {
          const commands = bPlusTreeAlgorithm.insertElement(key);
          if (!settings.isAnimationEnabled) {
            await commandExecutor.executeCommands(commands);
          }
        }
      }
    };

    initializeTree();
  }, [initialKeys, settings.order, bPlusTreeAlgorithm, commandExecutor, settings.isAnimationEnabled]);

  // 输入验证
  const validateInput = (value: string): boolean => {
    const num = parseInt(value);
    return !isNaN(num) && num > 0 && num <= 999;
  };

  // 插入处理函数
  const handleInsert = async () => {
    if (!validateInput(insertValue)) {
      messageApi.warning('请输入有效的正整数（1-999）');
      return;
    }

    const key = parseInt(insertValue);
    setError('');

    if (bPlusTreeAlgorithm.find(key)) {
      messageApi.warning(`键 ${key} 已存在，无法插入`);
      return;
    }

    try {
      const commands = bPlusTreeAlgorithm.insertElement(key);
      
      if (settings.isAnimationEnabled) {
        animationManager.loadCommands(commands);
        await animationManager.playAll();
      } else {
        await commandExecutor.executeCommands(commands);
        messageApi.success(`成功插入键 ${key}`);
      }

      setInsertValue('');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '插入失败';
      setError(errorMessage);
      messageApi.error(errorMessage);
    }
  };

  // 删除处理函数
  const handleDelete = async () => {
    if (!validateInput(deleteValue)) {
      messageApi.warning('请输入有效的正整数（1-999）');
      return;
    }

    const key = parseInt(deleteValue);
    setError('');

    if (!bPlusTreeAlgorithm.find(key)) {
      messageApi.warning(`键 ${key} 不存在，无法删除`);
      return;
    }

    try {
      const commands = bPlusTreeAlgorithm.deleteElement(key);
      
      if (settings.isAnimationEnabled) {
        animationManager.loadCommands(commands);
        await animationManager.playAll();
      } else {
        await commandExecutor.executeCommands(commands);
        messageApi.success(`成功删除键 ${key}`);
      }

      setDeleteValue('');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '删除失败';
      setError(errorMessage);
      messageApi.error(errorMessage);
    }
  };

  // 重置处理函数
  const handleReset = () => {
    animationManager.stop();
    commandExecutor.reset();
    bPlusTreeAlgorithm.clear();
    setInsertValue('');
    setDeleteValue('');
    setError('');
    messageApi.info('B+树已重置');
  };

  // 动画控制函数
  const handlePlay = () => animationManager.playAll();
  const handlePause = () => animationManager.pause();
  const handleStop = () => animationManager.stop();
  const handleStepForward = () => animationManager.stepForward();
  const handleStepBackward = () => animationManager.stepBackward();
  const handleJumpToStep = (step: number) => animationManager.jumpToStep(step);
  const handleSpeedChange = (speed: number) => {
    animationManager.setSpeed(speed);
    setSettings(prev => ({ ...prev, animationSpeed: speed }));
  };
  const handleAnimationReset = () => animationManager.reset();
  const handleJumpToNextBreakpoint = () => animationManager.jumpToNextBreakpoint();
  const handleJumpToPreviousBreakpoint = () => animationManager.jumpToPreviousBreakpoint();

  // 获取断点位置
  const breakpoints = animationManager.getStepBreakpoints();

  return (
    <ReactFlowProvider>
      {contextHolder}
      <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
        {/* 顶部控制区域 */}
        <Paper elevation={1} sx={{ p: 2, mb: 1 }}>
          <Grid container spacing={2} alignItems="center">
            {/* 插入控件 */}
            <Grid item xs={12} md={3}>
              <Box display="flex" gap={1}>
                <TextField
                  size="small"
                  label="插入值"
                  value={insertValue}
                  onChange={(e) => setInsertValue(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleInsert()}
                  disabled={isAnimating}
                  error={insertValue !== '' && !validateInput(insertValue)}
                  helperText={insertValue !== '' && !validateInput(insertValue) ? '请输入1-999的整数' : ''}
                />
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={handleInsert}
                  disabled={isAnimating || !validateInput(insertValue)}
                  color="primary"
                >
                  插入
                </Button>
              </Box>
            </Grid>

            {/* 删除控件 */}
            <Grid item xs={12} md={3}>
              <Box display="flex" gap={1}>
                <TextField
                  size="small"
                  label="删除值"
                  value={deleteValue}
                  onChange={(e) => setDeleteValue(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleDelete()}
                  disabled={isAnimating}
                  error={deleteValue !== '' && !validateInput(deleteValue)}
                  helperText={deleteValue !== '' && !validateInput(deleteValue) ? '请输入1-999的整数' : ''}
                />
                <Button
                  variant="contained"
                  startIcon={<DeleteIcon />}
                  onClick={handleDelete}
                  disabled={isAnimating || !validateInput(deleteValue)}
                  color="secondary"
                >
                  删除
                </Button>
              </Box>
            </Grid>

            {/* 重置按钮 */}
            <Grid item xs={12} md={2}>
              <Button
                variant="outlined"
                onClick={handleReset}
                disabled={isAnimating}
                fullWidth
              >
                重置
              </Button>
            </Grid>

            {/* 设置面板 */}
            <Grid item xs={12} md={4}>
              <SettingsPanel
                settings={settings}
                onSettingsChange={setSettings}
                disabled={isAnimating}
              />
            </Grid>
          </Grid>

          {/* 错误提示 */}
          {error && (
            <Alert severity="error" sx={{ mt: 2 }}>
              {error}
            </Alert>
          )}
        </Paper>

        {/* 动画控制面板 */}
        {settings.isAnimationEnabled && (
          <Box sx={{ mb: 1 }}>
            <AnimationControls
              animationState={animationState}
              onPlay={handlePlay}
              onPause={handlePause}
              onStop={handleStop}
              onStepForward={handleStepForward}
              onStepBackward={handleStepBackward}
              onJumpToStep={handleJumpToStep}
              onSpeedChange={handleSpeedChange}
              onReset={handleAnimationReset}
              onJumpToNextBreakpoint={handleJumpToNextBreakpoint}
              onJumpToPreviousBreakpoint={handleJumpToPreviousBreakpoint}
              breakpoints={breakpoints}
              disabled={false}
            />
          </Box>
        )}

        {/* React Flow 画布 */}
        <Box sx={{ flex: 1, position: 'relative' }}>
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            nodeTypes={nodeTypes}
            fitView
            fitViewOptions={{
              padding: 0.2,
              includeHiddenNodes: false,
            }}
            minZoom={0.1}
            maxZoom={2}
            defaultViewport={{ x: 0, y: 0, zoom: 1 }}
          >
            <Controls
              className={styles['bplus-controls']}
              showZoom={true}
              showFitView={true}
              showInteractive={true}
            />
            <Background
              variant={BackgroundVariant.Dots}
              gap={20}
              size={1}
              className={styles['bplus-background']}
            />
          </ReactFlow>
        </Box>
      </Box>
    </ReactFlowProvider>
  );
};

export default BPlusTreeVisualizerNew;
