/**
 * 重构后的B+树可视化组件
 * 基于指令序列的动画系统，实现算法与可视化的完全分离
 */

import React, { useEffect, useState, useCallback, useRef } from 'react';
import {
  ReactFlow,
  useNodesState,
  useEdgesState,
  Controls,
  Background,
  Node,
  Edge,
  BackgroundVariant,
  ReactFlowProvider
} from '@xyflow/react';
import {
  Box,
  Paper,
  TextField,
  Button,
  Typography,
  Alert,
  Stack,
  Divider,
  Snackbar
} from '@mui/material';
import { Add as AddIcon, Delete as DeleteIcon } from '@mui/icons-material';

// 导入新的重构组件
import { BPlusTreeAlgorithm } from '../../lib/bplus-tree/algorithm';
import { AnimationManager, AnimationState, AnimationCallbacks } from '../../lib/bplus-tree/animationManager';
import { CommandExecutor, CommandExecutorCallbacks } from '../../lib/bplus-tree/commandExecutor';
import { BPlusCommand } from '../../lib/bplus-tree/commands';
import AnimationControls from './AnimationControls';
import BPlusInternalNode from './BPlusInternalNode';
import BPlusLeafNode from './BPlusLeafNode';
import SettingsPanel from './SettingsPanel';
import { BPlusNodeData } from '../utils/bPlusTreeToReactFlow';
import styles from './BPlusTreeVisualizer.module.css';
import '@xyflow/react/dist/style.css';

// 自定义节点类型
const nodeTypes = {
  bPlusInternalNode: BPlusInternalNode,
  bPlusLeafNode: BPlusLeafNode,
};

interface BPlusTreeVisualizerNewProps {
  initialKeys: (number | string)[];
  order: number;
}

// 设置接口
interface Settings {
  isAnimationEnabled: boolean;
  animationSpeed: number;
  order: number;
}

const BPlusTreeVisualizerNew: React.FC<BPlusTreeVisualizerNewProps> = ({
  initialKeys,
  order
}) => {
  // 状态管理
  const [settings, setSettings] = useState<Settings>({
    isAnimationEnabled: true,
    animationSpeed: 500,
    order: order
  });

  const [insertValue, setInsertValue] = useState<string>('');
  const [deleteValue, setDeleteValue] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [isAnimating, setIsAnimating] = useState<boolean>(false);

  // Material-UI消息状态
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'info' | 'warning' | 'error';
  }>({
    open: false,
    message: '',
    severity: 'info'
  });

  // React Flow状态
  const [nodes, setNodes, onNodesChange] = useNodesState<Node<BPlusNodeData>>([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState<Edge>([]);

  // 动画系统状态
  const [animationState, setAnimationState] = useState<AnimationState>({
    currentStep: 0,
    totalSteps: 0,
    isPlaying: false,
    isPaused: false,
    speed: 500
  });

  // Material-UI消息处理函数
  const showMessage = useCallback((message: string, severity: 'success' | 'info' | 'warning' | 'error' = 'info') => {
    setSnackbar({
      open: true,
      message,
      severity
    });
  }, []);

  // 用 useRef 持久化核心实例
  const bPlusTreeAlgorithmRef = useRef<BPlusTreeAlgorithm | null>(null);
  const animationManagerRef = useRef<AnimationManager | null>(null);
  const commandExecutorRef = useRef<CommandExecutor | null>(null);

  // 初始化核心实例，只在 order 变化时重建
  useEffect(() => {
    bPlusTreeAlgorithmRef.current = new BPlusTreeAlgorithm(order);
    animationManagerRef.current = new AnimationManager();
    commandExecutorRef.current = new CommandExecutor({
      setNodes,
      setEdges,
      showMessage
    });
  }, [order, setNodes, setEdges, showMessage]);

  // 动画管理器回调
  useEffect(() => {
    if (!animationManagerRef.current || !commandExecutorRef.current) return;
    animationManagerRef.current.setCallbacks({
      onStepChange: async (step, command) => {
        if (command) {
          await commandExecutorRef.current!.executeCommand(command);
        }
      },
      onStateChange: (state) => {
        setAnimationState(state);
        setIsAnimating(state.isPlaying);
      },
      onComplete: () => {
        showMessage('动画播放完成', 'success');
        setIsAnimating(false);
      },
      onError: (error) => {
        showMessage(`动画执行错误: ${error.message}`, 'error');
        setIsAnimating(false);
        setError(error.message);
      }
    });
    animationManagerRef.current.setSpeed(settings.animationSpeed);
  }, [settings.animationSpeed, showMessage]);

  // 初始化B+树
  useEffect(() => {
    const initializeTree = async () => {
      commandExecutorRef.current?.reset();
      // 插入初始键值
      for (const key of initialKeys) {
        if (typeof key === 'number') {
          const commands = bPlusTreeAlgorithmRef.current!.insertElement(key);
          if (!settings.isAnimationEnabled) {
            await commandExecutorRef.current!.executeCommands(commands);
          }
        }
      }
    };
    initializeTree();
  }, [initialKeys, order, settings.isAnimationEnabled]);

  // 输入验证
  const validateInput = (value: string): boolean => {
    const num = parseInt(value);
    return !isNaN(num) && num > 0 && num <= 999;
  };

  // 插入处理函数
  const handleInsert = async () => {
    if (!validateInput(insertValue)) {
      showMessage('请输入有效的正整数（1-999）', 'warning');
      return;
    }
    const key = parseInt(insertValue);
    setError('');
    if (bPlusTreeAlgorithmRef.current!.find(key)) {
      showMessage(`键 ${key} 已存在，无法插入`, 'warning');
      return;
    }
    try {
      const commands = bPlusTreeAlgorithmRef.current!.insertElement(key);
      if (settings.isAnimationEnabled) {
        animationManagerRef.current!.loadCommands(commands);
        await animationManagerRef.current!.playAll();
      } else {
        await commandExecutorRef.current!.executeCommands(commands);
        showMessage(`成功插入键 ${key}`, 'success');
      }
      setInsertValue('');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '插入失败';
      setError(errorMessage);
      showMessage(errorMessage, 'error');
    }
  };

  // 删除处理函数
  const handleDelete = async () => {
    if (!validateInput(deleteValue)) {
      showMessage('请输入有效的正整数（1-999）', 'warning');
      return;
    }

    const key = parseInt(deleteValue);
    setError('');

    if (!bPlusTreeAlgorithmRef.current!.find(key)) {
      showMessage(`键 ${key} 不存在，无法删除`, 'warning');
      return;
    }

    try {
      const commands = bPlusTreeAlgorithmRef.current!.deleteElement(key);
      
      if (settings.isAnimationEnabled) {
        animationManagerRef.current!.loadCommands(commands);
        await animationManagerRef.current!.playAll();
      } else {
        await commandExecutorRef.current!.executeCommands(commands);
        showMessage(`成功删除键 ${key}`, 'success');
      }

      setDeleteValue('');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '删除失败';
      setError(errorMessage);
      showMessage(errorMessage, 'error');
    }
  };

  // 重置处理函数
  const handleReset = () => {
    animationManagerRef.current!.stop();
    commandExecutorRef.current!.reset();
    bPlusTreeAlgorithmRef.current!.clear();
    setInsertValue('');
    setDeleteValue('');
    setError('');
    showMessage('B+树已重置', 'info');
  };

  // 动画控制函数
  const handlePlay = () => animationManagerRef.current!.playAll();
  const handlePause = () => animationManagerRef.current!.pause();
  const handleStop = () => animationManagerRef.current!.stop();
  const handleStepForward = () => animationManagerRef.current!.stepForward();
  const handleStepBackward = () => animationManagerRef.current!.stepBackward();
  const handleJumpToStep = (step: number) => animationManagerRef.current!.jumpToStep(step);
  const handleSpeedChange = (speed: number) => {
    animationManagerRef.current!.setSpeed(speed);
    setSettings(prev => ({ ...prev, animationSpeed: speed }));
  };
  const handleAnimationReset = () => animationManagerRef.current!.reset();
  const handleJumpToNextBreakpoint = () => animationManagerRef.current!.jumpToNextBreakpoint();
  const handleJumpToPreviousBreakpoint = () => animationManagerRef.current!.jumpToPreviousBreakpoint();

  // 获取断点位置
  const breakpoints = animationManagerRef.current?.getStepBreakpoints() ?? [];

  // Snackbar关闭处理函数
  const handleSnackbarClose = useCallback((_event?: React.SyntheticEvent | Event, reason?: string) => {
    if (reason === 'clickaway') {
      return;
    }
    setSnackbar(prev => ({ ...prev, open: false }));
  }, []);

  return (
    <ReactFlowProvider>
      <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
        {/* 顶部控制区域 */}
        <Paper elevation={1} sx={{ p: 2, mb: 1 }}>
          <Stack spacing={2}>
            <Stack direction={{ xs: 'column', md: 'row' }} spacing={2} alignItems="center">
              {/* 插入控件 */}
              <Box display="flex" gap={1} sx={{ minWidth: 250 }}>
                <TextField
                  size="small"
                  label="插入值"
                  value={insertValue}
                  onChange={(e) => setInsertValue(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleInsert()}
                  disabled={isAnimating}
                  error={insertValue !== '' && !validateInput(insertValue)}
                  helperText={insertValue !== '' && !validateInput(insertValue) ? '请输入1-999的整数' : ''}
                />
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={handleInsert}
                  disabled={isAnimating || !validateInput(insertValue)}
                  color="primary"
                >
                  插入
                </Button>
              </Box>

              {/* 删除控件 */}
              <Box display="flex" gap={1} sx={{ minWidth: 250 }}>
                <TextField
                  size="small"
                  label="删除值"
                  value={deleteValue}
                  onChange={(e) => setDeleteValue(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleDelete()}
                  disabled={isAnimating}
                  error={deleteValue !== '' && !validateInput(deleteValue)}
                  helperText={deleteValue !== '' && !validateInput(deleteValue) ? '请输入1-999的整数' : ''}
                />
                <Button
                  variant="contained"
                  startIcon={<DeleteIcon />}
                  onClick={handleDelete}
                  disabled={isAnimating || !validateInput(deleteValue)}
                  color="secondary"
                >
                  删除
                </Button>
              </Box>

              {/* 重置按钮 */}
              <Button
                variant="outlined"
                onClick={handleReset}
                disabled={isAnimating}
                sx={{ minWidth: 100 }}
              >
                重置
              </Button>

              {/* 设置面板 */}
              <Box sx={{ flex: 1, minWidth: 300 }}>
                <SettingsPanel
                  settings={settings}
                  onSettingsChange={setSettings}
                  disabled={isAnimating}
                />
              </Box>
            </Stack>
          </Stack>

          {/* 错误提示 */}
          {error && (
            <Alert severity="error" sx={{ mt: 2 }}>
              {error}
            </Alert>
          )}
        </Paper>

        {/* 动画控制面板 */}
        {settings.isAnimationEnabled && (
          <Box sx={{ mb: 1 }}>
            <AnimationControls
              animationState={animationState}
              onPlay={handlePlay}
              onPause={handlePause}
              onStop={handleStop}
              onStepForward={handleStepForward}
              onStepBackward={handleStepBackward}
              onJumpToStep={handleJumpToStep}
              onSpeedChange={handleSpeedChange}
              onReset={handleAnimationReset}
              onJumpToNextBreakpoint={handleJumpToNextBreakpoint}
              onJumpToPreviousBreakpoint={handleJumpToPreviousBreakpoint}
              breakpoints={breakpoints}
              disabled={false}
            />
          </Box>
        )}

        {/* React Flow 画布 */}
        <Box sx={{ flex: 1, position: 'relative' }}>
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            nodeTypes={nodeTypes}
            fitView
            fitViewOptions={{
              padding: 0.2,
              includeHiddenNodes: false,
            }}
            minZoom={0.1}
            maxZoom={2}
            defaultViewport={{ x: 0, y: 0, zoom: 1 }}
          >
            <Controls
              className={styles['bplus-controls']}
              showZoom={true}
              showFitView={true}
              showInteractive={true}
            />
            <Background
              variant={BackgroundVariant.Dots}
              gap={20}
              size={1}
              className={styles['bplus-background']}
            />
          </ReactFlow>
        </Box>
      </Box>

      {/* Material-UI Snackbar for messages */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={4000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbar.severity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </ReactFlowProvider>
  );
};

export default BPlusTreeVisualizerNew;
