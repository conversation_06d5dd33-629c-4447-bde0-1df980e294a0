.historyPanel {
  height: 100%;
  min-height: 80vh;
  display: flex;
  flex-direction: column;
  padding-top: 1em;
  overflow: hidden;
  background-color: var(--sidebar-bg);
  border-radius: 8px;
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.03);
}

/* 添加 Tabs 相关样式 */
.tabsContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.tabsContainer :global(.ant-tabs) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.tabsContainer :global(.ant-tabs-nav) {
  margin: 0 !important;
  padding: 0 16px;
  margin-bottom: 8px!important;
}

.tabsContainer :global(.ant-tabs-nav-list) {
  width: 100%;
  display: flex !important;
}

.tabsContainer :global(.ant-tabs-tab) {
  flex: 1;
  display: flex;
  justify-content: center;
  margin: 0 !important;
}

.tabsContainer :global(.ant-tabs-content-holder) {
  flex: 1;
  overflow: hidden;
}

.tabsContainer :global(.ant-tabs-content) {
  height: 100%;
}

.headerContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  gap: 8px;
}

.searchContainer {
  flex: 1;
}

.list {
  overflow-y: auto;
  height: 100%;
  padding: 0;
  scrollbar-width: thin;
  scrollbar-color: #d0d0d0 transparent;
}

.list::-webkit-scrollbar {
  width: 6px;
}

.list::-webkit-scrollbar-track {
  background: transparent;
}

.list::-webkit-scrollbar-thumb {
  background-color: #d0d0d0;
  border-radius: 6px;
}

.spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  color: #1677ff;
}

.empty {
  margin-top: 20vh;
  color: var(--tertiary-text) !important;
}

/* 添加针对 Ant Design Empty 组件的样式覆盖 */
.empty :global(.ant-empty-description) {
  color: var(--tertiary-text) !important;
}


.ant-tabs-nav{
  margin-bottom: 16px !important;
}

.ant-tabs-tab{
  padding: 8px 16px !important;
  transition: all 0.3s ease;
}

.ant-tabs-tab-active {
  background-color: rgba(22, 119, 255, 0.1);
  border-radius: 4px;
}

.ant-tabs-ink-bar {
  background: var(--link-color) !important;
  height: 3px !important;
  border-radius: 3px !important;
}


.actionButton {
  width: 30px !important;
  height: 30px !important;
  font-size: 1.2em!important;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  transition: all 0.3s ease;
  color: var(--icon-color) !important;
  background: transparent;
}

.actionButton:hover {
  background: var(--button-hover) !important;
  color: var(--icon-color-hover) !important;
}

[data-theme="dark"] .actionButton {
  color: var(--icon-color-dark) !important;
}

[data-theme="dark"] .actionButton:hover {
  color: var(--icon-color-hover) !important;
}

.container {
  height: 100%;
  overflow-y: auto;
  padding: 16px;
  background-color: var(--background);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--divider-color);
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: var(--primary-text);
}

.recordList {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.recordItem {
  padding: 12px;
  border-radius: 8px;
  background-color: var(--card-bg);
  border: 1px solid var(--card-border);
  cursor: pointer;
  transition: all 0.2s ease;
}

.recordItem:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

[data-theme="dark"] .recordItem:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.recordTitle {
  font-weight: 500;
  margin-bottom: 4px;
  color: var(--primary-text);
}

.recordDate {
  font-size: 12px;
  color: var(--tertiary-text);
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--tertiary-text);
  text-align: center;
  padding: 20px;
}

.emptyIcon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
  color: var(--tertiary-text) !important;
}

.emptyText {
  font-size: 16px;
  max-width: 240px;
  color: var(--tertiary-text) !important;
}

.searchInput {
  width: 100%;
  margin-bottom: 16px;
  background-color: var(--input-bg) !important;
  color: var(--input-text) !important;
  border-color: var(--input-border) !important;
}

.searchInput input {
  color: var(--input-text) !important;
}

.searchInput:hover {
  border-color: var(--link-color) !important;
}

.searchInput:focus-within {
  border-color: var(--link-color) !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

[data-theme="dark"] .searchInput:focus-within {
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.3);
}
