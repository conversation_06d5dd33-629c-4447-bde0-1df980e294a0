.historyItem {
  padding: 0px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  background-color: var(--card-bg);
  border-radius: 10px!important;
  border-bottom: 1px solid var(--divider-color);
  margin-left: 6px;
  margin-right: 6px;
}

.historyItem:hover {
  background-color: var(--button-hover);
}

.active {
  background-color: var(--button-hover);
}

.titleContainer {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.title {
  font-size: 14px;
  color: var(--primary-text);
  padding-left: 6px;
  width: 100%;
}

.infoContainer {
  display: flex;
  align-items: center;
  padding-left: 6px;
  gap: 8px;
  font-size: 12px;
  color: var(--tertiary-text);
}

.dateInfo {
  display: flex;
  align-items: center;
  gap: 4px;
}

.tagsContainer {
  display: flex;
  gap: 2px;
}

.tag {
  font-size: 11px;
  padding: 0 4px;
  font-family: var(--font-mono) !important;
  line-height: 16px;
}

.actions {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  gap: 6px;
  opacity: 0;
  transition: all 0.3s ease;
}

.historyItem:hover .actions {
  opacity: 1;
}

.actions button {
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.actions button:hover {
  background-color: rgba(0, 0, 0, 0.05);
  transform: scale(1.05);
}

.editContainer {
  display: flex;
  width: 100%;
  gap: 8px;
  align-items: center;
}

.editContainer input {
  flex: 1;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  padding: 4px 8px;
  transition: all 0.3s;
}

.editContainer input:focus {
  border-color: #1677ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  outline: none;
}

/* 删除确认对话框样式 */
.deleteModal {
  width: 320px !important;
}

.tutorial {
  background-color: rgba(24, 144, 255, 0.1);
  border-left: 3px solid #1890ff;
}

.tutorial:hover {
  background-color: rgba(24, 144, 255, 0.2);
}

.moreButton{
  opacity: 0;
  transition: all 0.3s ease;
  font-size: 1.3em;
  margin-right: 6px;
}

.historyItem:hover .moreButton{
  opacity: 1;
}

/* dropdown的暗色模式适配样式 */

/* 添加一个全局样式容器类 */
.globalStylesContainer {
  /* 可以为空，仅用于包装全局样式 */
}

/* 下拉菜单适配暗色模式 */
.globalStylesContainer [data-theme="dark"] :global(.ant-dropdown-menu) {
  background-color: #1f1f1f !important;
  border-color: #333 !important;
}

/* 菜单项文字颜色 */
.globalStylesContainer [data-theme="dark"] :global(.ant-dropdown-menu-item) {
  color: #e0e0e0 !important;
}

/* 菜单项悬停样式 */
.globalStylesContainer [data-theme="dark"] :global(.ant-dropdown-menu-item:hover) {
  background-color: #333 !important;
}

/* 危险操作菜单项 */
.globalStylesContainer [data-theme="dark"] :global(.ant-dropdown-menu-item-danger) {
  color: #ff7875 !important;
}

/* 确认删除对话框 */
.globalStylesContainer [data-theme="dark"] :global(.ant-modal-content),
.globalStylesContainer [data-theme="dark"] :global(.ant-modal-header) {
  background-color: #1f1f1f !important;
  border-color: #333 !important;
}

.globalStylesContainer [data-theme="dark"] :global(.ant-modal-title),
.globalStylesContainer [data-theme="dark"] :global(.ant-modal-body) {
  color: #e0e0e0 !important;
}
