.resultContainer {
  height: 100%;
  overflow: auto;
  background-color: var(--background);
}

.resultTable {
  width: 100%;
  border-collapse: collapse;
  border-radius: 8px;
  overflow:auto;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .resultTable {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.resultTable th {
  background-color: var(--card-bg);
  color: var(--primary-text);
  font-weight: 600;
  text-align: left;
  padding: 12px 16px;
  border-bottom: 2px solid var(--divider-color);
}

.resultTable td {
  padding: 10px 16px;
  border-bottom: 1px solid var(--divider-color);
  color: var(--secondary-text);
}

.resultTable tr:nth-child(even) {
  background-color: var(--card-bg);
}

.resultTable tr:hover {
  background-color: var(--button-hover);
}

.emptyResult {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--tertiary-text);
  text-align: center;
}

.emptyIcon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.resultHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0;
  padding: 16px;
  background-color: var(--card-bg);
  border-bottom: 1px solid var(--divider-color);
}

.resultIcon {
  color: var(--icon-color);
  margin-right: 8px;
}

.resultTitle {
  font-size: 18px;
  font-weight: 600;
  color: var(--primary-text);
  display: flex;
  align-items: center;
  flex: 1;
}

.resultCount {
  color: var(--tertiary-text);
  font-size: 14px;
  margin-left: auto;
}

.tableContainer { 
  height: calc(100% - 56px); /* 减去头部高度 */
  max-height: calc(100vh - 200px); /* 设置最大高度，防止超出视图 */
  background-color: var(--card-bg);
  overflow: auto; /* 确保内容溢出时可以滚动 */
  position: relative; /* 添加相对定位 */
}

.nullValue {
  color: var(--tertiary-text);
  font-style: italic;
}
