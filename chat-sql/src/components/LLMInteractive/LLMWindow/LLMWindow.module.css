.container {
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  background: transparent;
}

.windowContainer {
  padding: 24px;
  border: none;
  border-radius: 0;
  box-shadow: none;
  height: 100%;
  display: flex;
  flex-direction: column;
  width: 100%;
  margin: 0 auto;
  transition: all 0.3s ease;
}

.contentWrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: center; /* 默认竖直居中显示 */
  gap: 20px; /* 添加间距 */
  transition: all 0.5s ease;
}

.withResultContent {
  justify-content: flex-start; /* 有结果时改为从顶部开始 */
}

.withResult {
  justify-content: flex-start; /* 有结果时改为从顶部开始 */
}

.headerArea {
  display: flex;
  align-items: center !important;
  justify-content: center!important;
  margin-bottom: 24px;
  padding-bottom: 12px;
  text-align: center;
  width: 100%;
  transition: all 0.5s ease;
}

.inputArea {
  margin-bottom: 0;
  display: flex;
  flex-direction: column;
  position: relative;
  margin-left: auto;
  margin-right: auto;
  width: 100%;
  transition: all 0.5s ease;
}

.inputAreaWithResult {
  margin-top: auto; /* 有结果时将输入区域推到底部 */
}

.textAreaWrapper {
  position: relative;
  margin-top: 10px;
  min-width: 45vw;
  margin-left: auto;
  margin-right: auto;
  color: var(--card-bg);
}

.buttonGroup {
  display: flex;
  align-items: center;
  margin-top: 12px;
  gap: 2em;
  justify-content: center; /* 确保按钮居中对齐 */
}

.actionButtonContainer {
  position: absolute;
  right: 8px;
  bottom: 24px;
  z-index: 2;
}

.resultArea {
  flex: 1;
  overflow: auto;
  margin-top: 16px;
  padding-top: 16px;
  max-height: calc(100% - 100px); /* 为输入区域留出空间 */
}

.chatBubble {
  background-color: var(--card-bg);
  border-radius: 18px;
  padding: 16px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  max-width: 95%;
  margin-left: auto;
  margin-right: auto;
  border: 1px solid var(--card-border);
}

.resultHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

/* 添加一个全局样式容器类 */
.globalStylesContainer {
  /* 可以为空，仅用于包装全局样式 */
}

/* 修复弹出框样式 */
.globalStylesContainer :global(.ant-popover-inner) {
  background-color: var(--card-bg) !important;
  border: 1px solid var(--card-border) !important;
}

.globalStylesContainer :global(.ant-popover-title),
.globalStylesContainer :global(.ant-popover-inner-content) {
  color: var(--primary-text) !important;
}

.globalStylesContainer :global(.ant-popover-arrow-content) {
  background-color: var(--card-bg) !important;
  border: 1px solid var(--card-border) !important;
}

/* 修复标签输入框样式 */
.tagInputContainer input {
  background-color: var(--input-bg) !important;
  color: var(--input-text) !important;
  border-color: var(--input-border) !important;
}

.tagInputContainer input::placeholder {
  color: var(--secondary-text) !important;
  opacity: 1 !important;
}
