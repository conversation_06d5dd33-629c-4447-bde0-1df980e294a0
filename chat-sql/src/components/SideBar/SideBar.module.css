.sideBarContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--sidebar-bg);
  border-right: 1px solid var(--sidebar-border);
  padding: 8px;
}

.topButtons, .bottomButtons {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 8px 0;
}

.topButtons {
  border-bottom: 1px solid var(--sidebar-border);
}

.bottomButtons {
  border-top: 1px solid var(--sidebar-border);
}

.actionButton {
  width: 2em !important;
  height: 2em!important;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  transition: all 0.3s ease;
  color: var(--icon-color) !important;
  background: transparent;
}

.actionButton:hover {
  background: var(--button-hover);
  color: var(--icon-color-hover) !important;
}

[data-theme="dark"] .actionButton {
  color: var(--icon-color-dark) !important;
}

[data-theme="dark"] .actionButton:hover {
  color: var(--icon-color-hover) !important;
}


.menuContainer {
  flex: 1;
  overflow-y: auto;
  margin-top:2em;
}

.menuItems {
  padding: 4px 0;
  font-size: 14px !important;
}

.bottomButtons {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 8px 0;
}

.bottomButtons button {
  width: 40px;
  height: 40px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

