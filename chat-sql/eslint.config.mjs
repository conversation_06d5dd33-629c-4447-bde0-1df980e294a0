import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  {
    ignores: ["src/cache/**/*"], // 忽略cache目录
  },
  ...compat.extends("next/core-web-vitals", "next/typescript"),
  {
    rules: {
      // 禁用所有报错的规则
      "@typescript-eslint/no-explicit-any": "off",
      "@typescript-eslint/no-unused-vars": "off",
      "@typescript-eslint/no-require-imports": "off",
      "@typescript-eslint/no-array-constructor": "off",
      "@typescript-eslint/no-unused-expressions": "off",
      "prefer-const": "off",
      "react/no-unescaped-entities": "off",
      "react-hooks/exhaustive-deps": "off",

      // 如果将来需要启用某些规则，可以设置为 "warn" 或 "error"
      // "@typescript-eslint/no-explicit-any": "warn",
    }
  }
];

export default eslintConfig;
